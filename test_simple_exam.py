#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for exam generation with exam code
"""

import requests
import json

# URL của API
BASE_URL = "http://localhost:8000"

def test_regular_exam():
    """Test regular exam generation endpoint"""
    print("=== Testing Regular Exam Generation with Exam Code ===")
    
    try:
        # Request data đơn giản
        request_data = {
            "ten_truong": "THPT Hong Thinh",
            "mon_hoc": "Hoa hoc",
            "lop": "12",
            "tong_so_cau": 5,
            "ma_de": "1234",  # Thêm mã đề cố định
            "cau_hinh_de": [
                {
                    "lesson_id": "test1",
                    "yeu_cau_can_dat": "Hieu duoc cau tao nguyen tu",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 3,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Thông hiểu",
                            "so_cau": 2,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
        
        # Gửi request
        response = requests.post(
            f"{BASE_URL}/api/v1/exam/generate-exam-download",
            json=request_data,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("Success!")
            print(f"Content-Type: {response.headers.get('content-type')}")
            print(f"Content-Length: {response.headers.get('content-length')}")
            
            # Lưu file để kiểm tra
            with open("test_exam_with_code.docx", "wb") as f:
                f.write(response.content)
            print("File saved as: test_exam_with_code.docx")
                
        else:
            print("Error!")
            print(f"Status: {response.status_code}")
            print(f"Headers: {response.headers}")
            
    except Exception as e:
        print(f"Exception: {str(e)}")

if __name__ == "__main__":
    test_regular_exam()
