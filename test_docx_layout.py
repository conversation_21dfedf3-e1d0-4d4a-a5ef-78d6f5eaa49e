#!/usr/bin/env python3
"""Test script để kiểm tra layout mới của cover page"""

import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.smart_exam_docx_service import SmartExamDocxService

async def test_cover_page_layout():
    """Test layout mới của cover page"""
    service = SmartExamDocxService()
    
    # Test data
    exam_request = {
        "subject": "Hóa học",
        "school": "TRƯỜNG THPT HONG THINH",
        "duration": 90,
        "examCode": "1234"
    }
    
    exam_data = {
        "questions": [
            {
                "part": 1,
                "question": "Câu hỏi test 1",
                "answer": {"A": "Đáp án A", "B": "Đáp án B", "C": "Đáp án C", "D": "Đáp án D"},
                "dap_an": {"dung": "A"}
            },
            {
                "part": 2,
                "question": "Câu hỏi test 2",
                "answer": {"a": {"content": "Statement A", "evaluation": "Đúng"}, 
                          "b": {"content": "Statement B", "evaluation": "Sai"},
                          "c": {"content": "Statement C", "evaluation": "Đúng"},
                          "d": {"content": "Statement D", "evaluation": "Sai"}},
                "dap_an": {"dung": "ĐSSĐ"}
            },
            {
                "part": 3,
                "question": "Câu hỏi test 3",
                "answer": "12.34",
                "dap_an": {"dung": "12,3"}
            }
        ],
        "statistics": {"total_questions": 3}
    }
    
    try:
        result = await service.create_smart_exam_docx(exam_data, exam_request)
        if result["success"]:
            print(f"✅ Tạo file DOCX thành công: {result['filename']}")
            print(f"📁 Đường dẫn: {result['file_path']}")
            print(f"📊 Kích thước: {result['file_size']} bytes")
            
            # Mở file để kiểm tra
            import subprocess
            subprocess.run(["start", result['file_path']], shell=True)
            
        else:
            print(f"❌ Lỗi tạo file: {result['error']}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_cover_page_layout())
