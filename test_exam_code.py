#!/usr/bin/env python3
"""
Test script để kiểm tra ô mã đề trong đề thi
"""

import requests
import json

# URL của API
BASE_URL = "http://localhost:8000"

def test_smart_exam_with_code():
    """Test tạo đề thi thông minh với mã đề"""
    
    # Data request với mã đề
    request_data = {
        "school": "Trường THPT Hong Thinh",
        "examCode": "0335",  # Mã đề như trong hình mẫu
        "grade": 12,
        "subject": "<PERSON><PERSON><PERSON> học",
        "examTitle": "Kiểm tra học kỳ I",
        "duration": 90,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": "test1",
                "totalQuestions": 6,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 2,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 1,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 0,
                            "Vận_dụng": 1
                        }
                    }
                ]
            }
        ]
    }
    
    print("=== Testing Smart Exam Generation with Exam Code ===")
    print(f"Request data: {json.dumps(request_data, indent=2, ensure_ascii=True)}")
    
    try:
        # Gửi request
        response = requests.post(
            f"{BASE_URL}/api/v1/exam/generate-smart-exam",
            json=request_data,
            timeout=120
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"Exam ID: {result.get('exam_id')}")
            print(f"Message: {result.get('message')}")
            
            # In links
            links = result.get('online_links', {})
            if links:
                print("\n📄 Document Links:")
                for link_type, url in links.items():
                    print(f"  {link_type}: {url}")
            
            # In statistics
            stats = result.get('statistics', {})
            if stats:
                print(f"\n📊 Statistics:")
                print(f"  Total questions: {stats.get('total_questions', 0)}")
                print(f"  Part I: {stats.get('part_1_questions', 0)}")
                print(f"  Part II: {stats.get('part_2_questions', 0)}")
                print(f"  Part III: {stats.get('part_3_questions', 0)}")
                
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_regular_exam_with_code():
    """Test tạo đề thi thông thường với mã đề"""
    
    request_data = {
        "exam_id": "test_exam_001",
        "mon_hoc": "Hóa học",
        "lop": 12,
        "ten_truong": "Trường THPT Hong Thinh",
        "ma_de": "0335",  # Thêm mã đề
        "tong_so_cau": 10,
        "cau_hinh_de": [
            {
                "lesson_id": "test1",
                "yeu_cau_can_dat": "Hiểu được cấu tạo nguyên tử",
                "muc_do": "Nhận biết",
                "so_cau": 5
            },
            {
                "lesson_id": "test1", 
                "yeu_cau_can_dat": "Vận dụng được kiến thức về liên kết hóa học",
                "muc_do": "Vận dụng",
                "so_cau": 5
            }
        ]
    }
    
    print("\n=== Testing Regular Exam Generation with Exam Code ===")
    print(f"Request data: {json.dumps(request_data, indent=2, ensure_ascii=True)}")
    
    try:
        # Gửi request
        response = requests.post(
            f"{BASE_URL}/api/v1/exam/generate-exam-download",
            json=request_data,
            timeout=120
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            print(f"Content-Type: {response.headers.get('content-type')}")
            print(f"Content-Length: {response.headers.get('content-length')}")
            
            # Lưu file để kiểm tra
            with open("test_exam_with_code.docx", "wb") as f:
                f.write(response.content)
            print("📄 File saved as: test_exam_with_code.docx")
                
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    # Test cả 2 loại đề thi
    test_smart_exam_with_code()
    test_regular_exam_with_code()
