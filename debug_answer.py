#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script để kiểm tra cấu trúc đáp án
"""

import requests
import json
import time

# Đợi server khởi động
time.sleep(5)

def test_simple_request():
    """Test với request đơn giản"""
    print("=== Testing Simple Request ===")
    
    try:
        # Request data tối thiểu
        request_data = {
            "exam_id": "test_exam_001",
            "ten_truong": "THPT Test",
            "mon_hoc": "Hoa hoc",
            "lop": "12",
            "tong_so_cau": 3,
            "ma_de": "1234",
            "cau_hinh_de": [
                {
                    "lesson_id": "test1",
                    "yeu_cau_can_dat": "Test requirement",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 3,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
        
        print("Sending request...")
        response = requests.post(
            "http://localhost:8000/api/v1/exam/generate-exam-download",
            json=request_data,
            timeout=120
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("Success! Saving file...")
            with open("debug_exam.docx", "wb") as f:
                f.write(response.content)
            print("File saved as: debug_exam.docx")
        else:
            print(f"Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"Raw response: {response.text}")
                
    except Exception as e:
        print(f"Exception: {str(e)}")

if __name__ == "__main__":
    test_simple_request()
