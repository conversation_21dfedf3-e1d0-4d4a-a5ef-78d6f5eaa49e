#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để test trực tiếp exam code layout
"""

from app.services.exam_docx_service import ExamDocxService
import tempfile
import os

def test_exam_code_layout():
    """Test layout mã đề trực tiếp"""
    print("=== Testing Exam Code Layout Directly ===")
    
    try:
        # Tạo service
        service = ExamDocxService()
        
        # Mock data
        exam_request = {
            "ten_truong": "THPT Hong Thinh",
            "mon_hoc": "Hoa hoc",
            "lop": "12",
            "tong_so_cau": 5,
            "ma_de": "1234"
        }
        
        exam_data = {
            "questions": [
                {
                    "stt": 1,
                    "loai_cau": "TN",
                    "muc_do": "Nhận biết",
                    "noi_dung_cau_hoi": "Câu hỏi test 1?",
                    "dap_an": {
                        "A": "Đáp án A",
                        "B": "<PERSON><PERSON><PERSON> án B", 
                        "C": "Đáp án C",
                        "D": "Đáp án D",
                        "dung": "A"
                    },
                    "giai_thich": "Đáp án A đúng vì...",
                    "bai_hoc": "test1",
                    "noi_dung_kien_thuc": "Kiến thức test"
                },
                {
                    "stt": 2,
                    "loai_cau": "TN",
                    "muc_do": "Thông hiểu",
                    "noi_dung_cau_hoi": "Câu hỏi test 2?",
                    "dap_an": {
                        "A": "Đáp án A",
                        "B": "Đáp án B", 
                        "C": "Đáp án C",
                        "D": "Đáp án D",
                        "dung": "B"
                    },
                    "giai_thich": "Đáp án B đúng vì...",
                    "bai_hoc": "test1",
                    "noi_dung_kien_thuc": "Kiến thức test"
                }
            ]
        }
        
        # Tạo file DOCX
        result = service.create_exam_docx(exam_request, exam_data)
        
        if result["success"]:
            print(f"Success! File created: {result['file_path']}")
            
            # Copy file ra thư mục hiện tại để kiểm tra
            import shutil
            dest_file = "test_exam_code_layout.docx"
            shutil.copy2(result['file_path'], dest_file)
            print(f"File copied to: {dest_file}")
            
            # Cleanup temp file
            if os.path.exists(result['file_path']):
                os.remove(result['file_path'])
                
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"Exception: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_exam_code_layout()
